# EKS Terraform Multi-Environment Setup

This repository contains a complete Terraform configuration for deploying AWS EKS clusters across multiple environments (development, staging, and production) with ArgoCD for GitOps.

## 🏗️ Architecture

The infrastructure includes:

- **Multi-environment setup**: Separate configurations for dev, staging, and production
- **VPC with proper networking**: Public/private subnets, NAT gateways, security groups
- **EKS cluster**: With proper IAM roles, encryption, and logging
- **EKS node groups**: Auto-scaling with different instance types per environment
- **ArgoCD**: Installed via Helm with RBAC and IRSA support
- **Security best practices**: Encryption at rest, IRSA, proper security groups

## 📁 Directory Structure

```
├── modules/
│   ├── vpc/                    # VPC module with networking
│   ├── eks-cluster/            # EKS cluster module
│   ├── eks-node-groups/        # EKS node groups module
│   └── argocd/                 # ArgoCD installation module
├── environments/
│   ├── dev/                    # Development environment
│   ├── staging/                # Staging environment
│   └── prod/                   # Production environment
├── main.tf                     # Root configuration (optional)
├── variables.tf                # Root variables
├── outputs.tf                  # Root outputs
└── README.md                   # This file
```

## 🚀 Quick Start

### Prerequisites

1. **AWS CLI** configured with appropriate permissions
2. **Terraform** >= 1.0 installed
3. **kubectl** installed for cluster management
4. **He<PERSON>** installed for ArgoCD management

### Required AWS Permissions

Your AWS credentials need the following permissions:
- EC2 (VPC, subnets, security groups, NAT gateways)
- EKS (cluster and node group management)
- IAM (roles and policies for EKS and IRSA)
- CloudWatch (log groups)
- KMS (encryption keys)

### Backend Configuration

1. Create an S3 bucket for Terraform state:
```bash
aws s3 mb s3://your-terraform-state-bucket
```

2. Create a DynamoDB table for state locking:
```bash
aws dynamodb create-table \
    --table-name terraform-state-lock \
    --attribute-definitions AttributeName=LockID,AttributeType=S \
    --key-schema AttributeName=LockID,KeyType=HASH \
    --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5
```

3. Update the backend configuration in each environment's `main.tf`:
```hcl
backend "s3" {
  bucket         = "your-terraform-state-bucket"
  key            = "eks/dev/terraform.tfstate"  # Change per environment
  region         = "us-west-2"
  dynamodb_table = "terraform-state-lock"
  encrypt        = true
}
```

## 🌍 Environment Deployment

### Development Environment

```bash
cd environments/dev
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values
terraform init
terraform plan
terraform apply
```

### Staging Environment

```bash
cd environments/staging
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values
terraform init
terraform plan
terraform apply
```

### Production Environment

```bash
cd environments/prod
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values
terraform init
terraform plan
terraform apply
```

## ⚙️ Configuration

### Environment-Specific Settings

| Setting | Development | Staging | Production |
|---------|-------------|---------|------------|
| Instance Types | t3.medium | t3.large | m5.xlarge, c5.2xlarge |
| Node Count | 2-4 | 3-6 | 5-10 |
| NAT Gateway | Single | Multi-AZ | Multi-AZ |
| Log Retention | 7 days | 14 days | 30 days |
| Public Access | Open | Restricted | Private networks only |

### Key Variables

- `cluster_version`: Kubernetes version (default: "1.28")
- `node_groups`: Map of node group configurations
- `vpc_cidr`: VPC CIDR block
- `enable_nat_gateway`: Enable NAT gateways for private subnets
- `argocd_server_service_type`: ArgoCD service type (LoadBalancer/ClusterIP)

## 🔐 Security Features

- **Encryption at rest**: EKS secrets encrypted with KMS
- **IRSA**: IAM Roles for Service Accounts for ArgoCD
- **Network security**: Proper security groups and NACLs
- **Private subnets**: Worker nodes in private subnets
- **Restricted access**: Production clusters with limited public access

## 📊 Accessing Your Cluster

### Configure kubectl

```bash
aws eks update-kubeconfig --region us-west-2 --name eks-demo-dev
```

### Access ArgoCD

1. **Get admin password**:
```bash
kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d
```

2. **Port forward** (if using ClusterIP):
```bash
kubectl port-forward svc/argocd-server -n argocd 8080:443
```

3. **Access via LoadBalancer** (if configured):
```bash
kubectl get svc argocd-server -n argocd
```

## 🔧 Customization

### Adding Node Groups

Add to your `terraform.tfvars`:

```hcl
node_groups = {
  general = {
    instance_types = ["t3.medium"]
    capacity_type  = "ON_DEMAND"
    desired_size   = 2
    max_size       = 4
    min_size       = 1
    disk_size      = 50
    labels = {
      role = "general"
    }
    taints = []
  }
  spot = {
    instance_types = ["t3.medium", "t3.large"]
    capacity_type  = "SPOT"
    desired_size   = 1
    max_size       = 3
    min_size       = 0
    disk_size      = 50
    labels = {
      role = "spot"
    }
    taints = [
      {
        key    = "spot"
        value  = "true"
        effect = "NO_SCHEDULE"
      }
    ]
  }
}
```

### ArgoCD Configuration

Customize ArgoCD in your `terraform.tfvars`:

```hcl
argocd_server_service_type = "LoadBalancer"  # or "ClusterIP"
argocd_load_balancer_scheme = "internet-facing"  # or "internal"
argocd_enable_irsa = true
```

## 🧹 Cleanup

To destroy an environment:

```bash
cd environments/dev  # or staging/prod
terraform destroy
```

## 📝 Notes

- **State Management**: Each environment has its own state file
- **Module Reusability**: All modules are reusable across environments
- **Security**: Production uses internal load balancers and restricted access
- **Scaling**: Node groups support auto-scaling based on demand
- **Monitoring**: CloudWatch logging enabled for all clusters

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test in development environment
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
