#!/bin/bash

# Setup Terraform Backend (S3 + DynamoDB)
# Usage: ./scripts/setup-backend.sh <bucket-name> <region> [dynamodb-table-name]

set -e

BUCKET_NAME=$1
REGION=${2:-us-west-2}
DYNAMODB_TABLE=${3:-terraform-state-lock}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

if [[ -z "$BUCKET_NAME" ]]; then
    print_error "Bucket name is required. Usage: $0 <bucket-name> [region] [dynamodb-table-name]"
    exit 1
fi

print_status "Setting up Terraform backend..."
print_status "Bucket: $BUCKET_NAME"
print_status "Region: $REGION"
print_status "DynamoDB Table: $DYNAMODB_TABLE"

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &>/dev/null; then
    print_error "AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

# Create S3 bucket
print_status "Creating S3 bucket: $BUCKET_NAME"
if aws s3api head-bucket --bucket "$BUCKET_NAME" 2>/dev/null; then
    print_warning "Bucket $BUCKET_NAME already exists"
else
    if [[ "$REGION" == "us-east-1" ]]; then
        aws s3api create-bucket --bucket "$BUCKET_NAME" --region "$REGION"
    else
        aws s3api create-bucket --bucket "$BUCKET_NAME" --region "$REGION" \
            --create-bucket-configuration LocationConstraint="$REGION"
    fi
    print_success "Created S3 bucket: $BUCKET_NAME"
fi

# Enable versioning
print_status "Enabling versioning on S3 bucket..."
aws s3api put-bucket-versioning --bucket "$BUCKET_NAME" \
    --versioning-configuration Status=Enabled
print_success "Enabled versioning on S3 bucket"

# Enable server-side encryption
print_status "Enabling server-side encryption on S3 bucket..."
aws s3api put-bucket-encryption --bucket "$BUCKET_NAME" \
    --server-side-encryption-configuration '{
        "Rules": [
            {
                "ApplyServerSideEncryptionByDefault": {
                    "SSEAlgorithm": "AES256"
                }
            }
        ]
    }'
print_success "Enabled server-side encryption on S3 bucket"

# Block public access
print_status "Blocking public access on S3 bucket..."
aws s3api put-public-access-block --bucket "$BUCKET_NAME" \
    --public-access-block-configuration \
    BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=true,RestrictPublicBuckets=true
print_success "Blocked public access on S3 bucket"

# Create DynamoDB table
print_status "Creating DynamoDB table: $DYNAMODB_TABLE"
if aws dynamodb describe-table --table-name "$DYNAMODB_TABLE" --region "$REGION" &>/dev/null; then
    print_warning "DynamoDB table $DYNAMODB_TABLE already exists"
else
    aws dynamodb create-table \
        --table-name "$DYNAMODB_TABLE" \
        --attribute-definitions AttributeName=LockID,AttributeType=S \
        --key-schema AttributeName=LockID,KeyType=HASH \
        --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
        --region "$REGION"
    
    print_status "Waiting for DynamoDB table to be active..."
    aws dynamodb wait table-exists --table-name "$DYNAMODB_TABLE" --region "$REGION"
    print_success "Created DynamoDB table: $DYNAMODB_TABLE"
fi

print_success "Backend setup completed successfully!"
echo ""
echo "=== Backend Configuration ==="
echo "Add this to your Terraform configuration:"
echo ""
echo "terraform {"
echo "  backend \"s3\" {"
echo "    bucket         = \"$BUCKET_NAME\""
echo "    key            = \"path/to/your/terraform.tfstate\""
echo "    region         = \"$REGION\""
echo "    dynamodb_table = \"$DYNAMODB_TABLE\""
echo "    encrypt        = true"
echo "  }"
echo "}"
echo ""
echo "=== Next Steps ==="
echo "1. Update the backend configuration in your Terraform files"
echo "2. Run 'terraform init' to initialize the backend"
echo "3. Your state will be stored in S3 with DynamoDB locking"
