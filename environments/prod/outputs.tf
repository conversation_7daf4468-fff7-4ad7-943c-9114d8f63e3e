# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "private_subnet_ids" {
  description = "List of IDs of private subnets"
  value       = module.vpc.private_subnet_ids
}

output "public_subnet_ids" {
  description = "List of IDs of public subnets"
  value       = module.vpc.public_subnet_ids
}

# EKS Cluster Outputs
output "cluster_id" {
  description = "The name/id of the EKS cluster"
  value       = module.eks_cluster.cluster_id
}

output "cluster_arn" {
  description = "The Amazon Resource Name (ARN) of the cluster"
  value       = module.eks_cluster.cluster_arn
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = module.eks_cluster.cluster_endpoint
}

output "cluster_version" {
  description = "The Kubernetes version for the EKS cluster"
  value       = module.eks_cluster.cluster_version
}

output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = module.eks_cluster.cluster_certificate_authority_data
}

output "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster OIDC Issuer"
  value       = module.eks_cluster.cluster_oidc_issuer_url
}

output "oidc_provider_arn" {
  description = "The ARN of the OIDC Provider"
  value       = module.eks_cluster.oidc_provider_arn
}

# Node Groups Outputs
output "node_groups" {
  description = "Map of node group outputs"
  value = {
    for k, v in module.eks_node_groups : k => {
      arn           = v.node_group_arn
      status        = v.node_group_status
      capacity_type = v.node_group_capacity_type
      version       = v.node_group_version
    }
  }
}

# ArgoCD Outputs
output "argocd_namespace" {
  description = "Kubernetes namespace where ArgoCD is deployed"
  value       = module.argocd.namespace
}

output "argocd_server_service_name" {
  description = "Name of the ArgoCD server service"
  value       = module.argocd.argocd_server_service_name
}

output "argocd_admin_password" {
  description = "ArgoCD admin password (base64 encoded)"
  value       = module.argocd.argocd_admin_password
  sensitive   = true
}

output "argocd_iam_role_arn" {
  description = "ARN of the IAM role for ArgoCD service account"
  value       = module.argocd.iam_role_arn
}

# kubectl configuration command
output "kubectl_config_command" {
  description = "Command to configure kubectl"
  value       = "aws eks update-kubeconfig --region ${var.aws_region} --name ${module.eks_cluster.cluster_id}"
}

# ArgoCD access information
output "argocd_access_info" {
  description = "Information on how to access ArgoCD"
  value = {
    service_type = var.argocd_server_service_type
    namespace    = module.argocd.namespace
    username     = "admin"
    password_command = "kubectl -n ${module.argocd.namespace} get secret argocd-initial-admin-secret -o jsonpath='{.data.password}' | base64 -d"
    port_forward_command = "kubectl port-forward svc/${module.argocd.argocd_server_service_name} -n ${module.argocd.namespace} 8080:443"
  }
}
