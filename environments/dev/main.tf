# Development Environment Configuration

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.23"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.11"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }
  }

  backend "s3" {
    bucket         = "dell-terraform-state-bucket-ap-southeast-1"
    key            = "eks/dev/terraform.tfstate"
    region         = "ap-southeast-1"
    dynamodb_table = "terraform-state-lock"
    encrypt        = true
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Environment   = "dev"
      Project       = var.project_name
      ManagedBy     = "terraform"
      Owner         = var.owner
    }
  }
}

# Data source for EKS cluster
data "aws_eks_cluster" "cluster" {
  name = module.eks_cluster.cluster_id
  depends_on = [module.eks_cluster]
}

data "aws_eks_cluster_auth" "cluster" {
  name = module.eks_cluster.cluster_id
  depends_on = [module.eks_cluster]
}

# Configure Kubernetes provider
provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

# Configure Helm provider
provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

# Local values
locals {
  cluster_name = "${var.project_name}-${var.environment}"
  
  common_tags = {
    Environment = var.environment
    Project     = var.project_name
    ManagedBy   = "terraform"
    Owner       = var.owner
  }
}

# VPC Module
module "vpc" {
  source = "../../modules/vpc"

  cluster_name         = local.cluster_name
  vpc_cidr            = var.vpc_cidr
  public_subnet_cidrs = var.public_subnet_cidrs
  private_subnet_cidrs = var.private_subnet_cidrs
  enable_nat_gateway   = var.enable_nat_gateway
  single_nat_gateway   = var.single_nat_gateway

  tags = local.common_tags
}

# EKS Cluster Module
module "eks_cluster" {
  source = "../../modules/eks-cluster"

  cluster_name                        = local.cluster_name
  cluster_version                     = var.cluster_version
  private_subnet_ids                  = module.vpc.private_subnet_ids
  public_subnet_ids                   = module.vpc.public_subnet_ids
  cluster_security_group_id           = module.vpc.cluster_security_group_id
  cluster_endpoint_private_access     = var.cluster_endpoint_private_access
  cluster_endpoint_public_access      = var.cluster_endpoint_public_access
  cluster_endpoint_public_access_cidrs = var.cluster_endpoint_public_access_cidrs
  cluster_enabled_log_types           = var.cluster_enabled_log_types
  cloudwatch_log_retention_in_days    = var.cloudwatch_log_retention_in_days

  # Add-ons
  enable_vpc_cni_addon     = var.enable_vpc_cni_addon
  enable_coredns_addon     = var.enable_coredns_addon
  enable_kube_proxy_addon  = var.enable_kube_proxy_addon
  enable_ebs_csi_addon     = var.enable_ebs_csi_addon

  tags = local.common_tags
}

# EKS Node Groups
module "eks_node_groups" {
  source = "../../modules/eks-node-groups"

  for_each = var.node_groups

  cluster_name                        = module.eks_cluster.cluster_id
  node_group_name                     = each.key
  cluster_endpoint                    = module.eks_cluster.cluster_endpoint
  cluster_certificate_authority_data = module.eks_cluster.cluster_certificate_authority_data
  subnet_ids                          = module.vpc.private_subnet_ids
  node_security_group_id              = module.vpc.node_security_group_id
  
  instance_types   = each.value.instance_types
  capacity_type    = each.value.capacity_type
  desired_size     = each.value.desired_size
  max_size         = each.value.max_size
  min_size         = each.value.min_size
  disk_size        = each.value.disk_size
  
  labels = each.value.labels
  taints = each.value.taints

  tags = local.common_tags

  depends_on = [module.eks_cluster]
}

# ArgoCD Module
module "argocd" {
  source = "../../modules/argocd"

  cluster_name        = local.cluster_name
  namespace           = var.argocd_namespace
  create_namespace    = var.argocd_create_namespace
  chart_version       = var.argocd_chart_version
  argocd_version      = var.argocd_version
  server_service_type = var.argocd_server_service_type
  load_balancer_scheme = var.argocd_load_balancer_scheme
  
  enable_irsa         = var.argocd_enable_irsa
  oidc_provider_arn   = module.eks_cluster.oidc_provider_arn
  oidc_issuer_url     = module.eks_cluster.cluster_oidc_issuer_url

  tags = local.common_tags

  depends_on = [module.eks_node_groups]
}
