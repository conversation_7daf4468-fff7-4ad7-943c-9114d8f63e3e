# Root Terraform Configuration
# This file can be used to deploy all environments or as a reference

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.23"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.11"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }
  }

  # Configure your backend here
  backend "s3" {
    bucket = "dell-terraform-state-bucket-ap-southeast-1"
    key    = "eks/root/terraform.tfstate"
    region = "ap-southeast-1"
    dynamodb_table = "terraform-state-lock"
    encrypt = true
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Project   = var.project_name
      ManagedBy = "terraform"
      Owner     = var.owner
    }
  }
}

# Example: Deploy development environment
# Uncomment and modify as needed

module "dev_environment" {
  source = "./environments/dev"
  
  # Override variables as needed
  aws_region   = var.aws_region
  project_name = var.project_name
  owner        = var.owner
}

# module "staging_environment" {
#   source = "./environments/staging"
#   
#   # Override variables as needed
#   aws_region   = var.aws_region
#   project_name = var.project_name
#   owner        = var.owner
# }

# module "prod_environment" {
#   source = "./environments/prod"
#   
#   # Override variables as needed
#   aws_region   = var.aws_region
#   project_name = var.project_name
#   owner        = var.owner
# }
