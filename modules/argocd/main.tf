# Data source for current AWS caller identity
data "aws_caller_identity" "current" {}

# Data source for AWS partition
data "aws_partition" "current" {}

# Kubernetes namespace for ArgoCD
resource "kubernetes_namespace" "argocd" {
  count = var.create_namespace ? 1 : 0

  metadata {
    name = var.namespace
    labels = {
      name = var.namespace
    }
  }
}

# IAM role for ArgoCD service account (IRSA)
resource "aws_iam_role" "argocd" {
  count = var.enable_irsa ? 1 : 0

  name = "${var.cluster_name}-argocd-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Federated = var.oidc_provider_arn
        }
        Condition = {
          StringEquals = {
            "${replace(var.oidc_issuer_url, "https://", "")}:sub" = "system:serviceaccount:${var.namespace}:${var.service_account_name}"
            "${replace(var.oidc_issuer_url, "https://", "")}:aud" = "sts.amazonaws.com"
          }
        }
      }
    ]
  })

  tags = var.tags
}

# IAM policy for ArgoCD (customize based on your needs)
resource "aws_iam_policy" "argocd" {
  count = var.enable_irsa ? 1 : 0

  name        = "${var.cluster_name}-argocd-policy"
  description = "IAM policy for ArgoCD"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret",
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:GetParametersByPath"
        ]
        Resource = "*"
      }
    ]
  })

  tags = var.tags
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "argocd" {
  count = var.enable_irsa ? 1 : 0

  policy_arn = aws_iam_policy.argocd[0].arn
  role       = aws_iam_role.argocd[0].name
}

# Kubernetes service account for ArgoCD
resource "kubernetes_service_account" "argocd" {
  count = var.enable_irsa ? 1 : 0

  metadata {
    name      = var.service_account_name
    namespace = var.create_namespace ? kubernetes_namespace.argocd[0].metadata[0].name : var.namespace
    annotations = {
      "eks.amazonaws.com/role-arn" = aws_iam_role.argocd[0].arn
    }
  }

  depends_on = [kubernetes_namespace.argocd]
}

# ArgoCD Helm release
resource "helm_release" "argocd" {
  name       = var.release_name
  repository = "https://argoproj.github.io/argo-helm"
  chart      = "argo-cd"
  version    = var.chart_version
  namespace  = var.create_namespace ? kubernetes_namespace.argocd[0].metadata[0].name : var.namespace

  # Basic configuration
  set {
    name  = "global.image.tag"
    value = var.argocd_version
  }

  # Server configuration
  set {
    name  = "server.service.type"
    value = var.server_service_type
  }

  dynamic "set" {
    for_each = var.server_service_type == "LoadBalancer" ? [1] : []
    content {
      name  = "server.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-type"
      value = "nlb"
    }
  }

  dynamic "set" {
    for_each = var.server_service_type == "LoadBalancer" ? [1] : []
    content {
      name  = "server.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-scheme"
      value = var.load_balancer_scheme
    }
  }

  # Enable ingress if specified
  dynamic "set" {
    for_each = var.enable_ingress ? [1] : []
    content {
      name  = "server.ingress.enabled"
      value = "true"
    }
  }

  dynamic "set" {
    for_each = var.enable_ingress && var.ingress_class_name != null ? [1] : []
    content {
      name  = "server.ingress.ingressClassName"
      value = var.ingress_class_name
    }
  }

  dynamic "set" {
    for_each = var.enable_ingress && length(var.ingress_hosts) > 0 ? var.ingress_hosts : []
    content {
      name  = "server.ingress.hosts[${set.key}]"
      value = set.value
    }
  }

  # RBAC configuration
  set {
    name  = "server.rbacConfig.policy\\.default"
    value = var.rbac_default_policy
  }

  set {
    name  = "server.rbacConfig.scopes"
    value = "[groups]"
  }

  # Repository server configuration
  set {
    name  = "repoServer.replicas"
    value = var.repo_server_replicas
  }

  # Application controller configuration
  set {
    name  = "controller.replicas"
    value = var.controller_replicas
  }

  # Redis configuration
  set {
    name  = "redis.enabled"
    value = var.enable_redis
  }

  # Dex configuration (for SSO)
  set {
    name  = "dex.enabled"
    value = var.enable_dex
  }

  # Service account configuration
  dynamic "set" {
    for_each = var.enable_irsa ? [1] : []
    content {
      name  = "server.serviceAccount.create"
      value = "false"
    }
  }

  dynamic "set" {
    for_each = var.enable_irsa ? [1] : []
    content {
      name  = "server.serviceAccount.name"
      value = kubernetes_service_account.argocd[0].metadata[0].name
    }
  }

  # Additional values
  values = var.additional_helm_values != null ? [var.additional_helm_values] : []

  depends_on = [
    kubernetes_namespace.argocd,
    kubernetes_service_account.argocd
  ]
}

# Get ArgoCD admin password
data "kubernetes_secret" "argocd_initial_admin_secret" {
  metadata {
    name      = "argocd-initial-admin-secret"
    namespace = var.create_namespace ? kubernetes_namespace.argocd[0].metadata[0].name : var.namespace
  }

  depends_on = [helm_release.argocd]
}
